import { sys } from 'cc';
import { UserData } from './UserData';

/**
 * 使用 GID 作为唯一标识符
 */
export class HiddenRoomStateManager {
    /**
     * 获取 GID key (格式: "x:y")
     */
    private static getGIDKey(gid: { x: number; y: number }) {
        return `${gid.x}:${gid.y}`;
    }

    public static saveEntranceDug(entranceGID: { x: number; y: number }) {
        const key = this.getGIDKey(entranceGID);
        if (!UserData.hiddenRooms[key]) UserData.hiddenRooms[key] = {};
        UserData.hiddenRooms[key].entranceDug = true;
    }

    /**
     * 检查入口是否被挖掘
     */
    public static isEntranceDug(entranceGID: { x: number; y: number }): boolean {
        const key = this.getGIDKey(entranceGID);
        const result = !!UserData.hiddenRooms[key]?.entranceDug;
        return result;
    }

    /**
     * 保存进入隐藏房间状态（持久化到 UserData）
     */
    public static saveEntered(entranceGID: { x: number; y: number }) {
        const key = this.getGIDKey(entranceGID);
        if (!UserData.hiddenRooms[key]) UserData.hiddenRooms[key] = {};
        UserData.hiddenRooms[key].entered = true;
    }

    /**
     * 检查是否进入过
     */
    public static isEntered(entranceGID: { x: number; y: number }): boolean {
        const key = this.getGIDKey(entranceGID);
        return !!UserData.hiddenRooms[key]?.entered;
    }

    /**
     * 保存宝箱开启状态（持久化到 UserData）
     */
    public static saveTreasureOpened(entranceGID: { x: number; y: number }) {
        const key = this.getGIDKey(entranceGID);
        if (!UserData.hiddenRooms[key]) UserData.hiddenRooms[key] = {};
        UserData.hiddenRooms[key].treasureOpened = true;
    }

    /**
     * 检查宝箱是否已开
     */
    public static isTreasureOpened(entranceGID: { x: number; y: number }): boolean {
        const key = this.getGIDKey(entranceGID);
        return !!UserData.hiddenRooms[key]?.treasureOpened;
    }

    /**
     * 获取完整状态
     */
    public static getRoomState(entranceGID: { x: number; y: number }) {
        const key = this.getGIDKey(entranceGID);
        const state = UserData.hiddenRooms[key] || {};
        const result = {
            key: key,
            entranceDug: !!state.entranceDug,
            entered: !!state.entered,
            treasureOpened: !!state.treasureOpened,
        };
        return result;
    }

    public static saveCurrentEntranceGID(entranceGID: { x: number; y: number }) {
        // 临时存储到独立的 localStorage key，不与 UserData 混合
        sys.localStorage.setItem('currentHiddenRoomEntranceGID', JSON.stringify(entranceGID));
    }

    /**
     * 获取当前进入的隐藏房间入口GID（临时存储）
     */
    public static getCurrentEntranceGID(): { x: number; y: number } | null {
        // 从临时存储的独立 key 读取
        const gidData = sys.localStorage.getItem('currentHiddenRoomEntranceGID');

        const result = gidData ? JSON.parse(gidData) : null;

        return result;
    }

    /**
     * 清除当前入口GID（临时存储）
     * 在玩家返回主场景并定位完成后调用
     */
    public static clearCurrentEntranceGID() {
        sys.localStorage.removeItem('currentHiddenRoomEntranceGID');
    }
}
