import { _decorator, Component, Node, Vec3, director, Graphics, UITransform, view, Color } from 'cc';
import { EnterRoomMask } from './EnterRoomMask';
const { ccclass, property } = _decorator;

/**
 * 测试进入隐藏房间动画的组件
 * 可以在编辑器中添加到任何节点上进行测试
 */
@ccclass('TestEnterRoomAnimation')
export class TestEnterRoomAnimation extends Component {
    
    @property(Node)
    testPlayer: Node = null;

    start() {
        console.log('[TEST] TestEnterRoomAnimation 组件已启动');
        
        // 如果没有指定测试玩家节点，创建一个
        if (!this.testPlayer) {
            this.createTestPlayer();
        }
    }

    /**
     * 创建一个测试用的玩家节点
     */
    private createTestPlayer() {
        this.testPlayer = new Node('TestPlayer');
        this.testPlayer.addComponent(UITransform);
        
        // 添加一个简单的图形作为玩家
        const graphics = this.testPlayer.addComponent(Graphics);
        graphics.fillColor = new Color(255, 0, 0, 255); // 红色
        graphics.fillRect(-20, -20, 40, 40);
        graphics.fill();
        
        // 设置玩家位置在屏幕中央
        this.testPlayer.setPosition(new Vec3(0, 0, 0));
        
        // 添加到场景
        const canvas = director.getScene().getChildByPath('Canvas');
        if (canvas) {
            canvas.addChild(this.testPlayer);
            console.log('[TEST] 已创建测试玩家节点');
        }
    }

    /**
     * 测试进入房间动画
     * 可以在编辑器中调用或通过代码调用
     */
    public testEnterRoomAnimation() {
        if (!this.testPlayer) {
            console.error('[TEST] 没有找到测试玩家节点');
            return;
        }

        console.log('[TEST] 🚀 开始测试进入房间动画');

        // 创建遮罩节点
        const maskNode = new Node('TestEnterRoomMask');
        maskNode.addComponent(UITransform);
        maskNode.addComponent(Graphics);

        // 创建Cover子节点
        const coverNode = new Node('Cover');
        coverNode.addComponent(UITransform);
        coverNode.addComponent(Graphics);
        maskNode.addChild(coverNode);

        // 添加到场景
        const canvas = director.getScene().getChildByPath('Canvas');
        if (!canvas) {
            console.error('[TEST] 没有找到Canvas节点');
            return;
        }
        canvas.addChild(maskNode);

        // 添加EnterRoomMask组件
        const enterRoomMask = maskNode.addComponent(EnterRoomMask);

        // 计算目标位置（玩家当前位置）
        const targetPos = this.testPlayer.position.clone();

        console.log(`[TEST] 玩家位置: (${targetPos.x}, ${targetPos.y})`);

        // 延迟一下确保组件初始化完成
        this.scheduleOnce(() => {
            // 开始动画
            enterRoomMask.startEnterAnimation(this.testPlayer, targetPos, () => {
                console.log('[TEST] ✅ 动画完成！');
                console.log('[TEST] 在实际游戏中，这里会切换到隐藏房间场景');
                
                // 清理测试节点
                this.scheduleOnce(() => {
                    maskNode.destroy();
                    console.log('[TEST] 测试完成，已清理遮罩节点');
                }, 1.0);
            });
        }, 0.1);
    }

    /**
     * 清理测试节点
     */
    public cleanup() {
        if (this.testPlayer) {
            this.testPlayer.destroy();
            this.testPlayer = null;
            console.log('[TEST] 已清理测试玩家节点');
        }
    }

    onDestroy() {
        this.cleanup();
    }
}
