import { _decorator, Color, Component, Graphics, UITransform, view, Vec3, tween, Node } from 'cc';
import { GameConfig } from './GameConfig';
const { ccclass } = _decorator;

@ccclass('EnterRoomMask')
export class EnterRoomMask extends Component {
    private coverG: Graphics; // 用于绘制圆形遮罩的Graphics组件
    private backgroundG: Graphics; // 用于绘制背景的Graphics组件
    private targetPos: Vec3;
    private player: Node;
    private currentRadius: number;
    private animationDuration = 1.5; // 动画持续时间，稍微缩短一点
    private finalRadius = 20; // 最终半径，稍微小一点让效果更明显
    private minPlayerScale = 0.2; // 玩家最小缩放比例，让缩小效果更明显
    private maskColor = new Color().fromHEX('#111111'); // 遮罩颜色

    public start() {
        const winSize = view.getVisibleSize();

        // 禁用节点的物理交互，防止影响玩家移动
        this.node.layer = 0; // 设置为UI层，避免物理碰撞

        // 确保节点不参与物理计算
        const uiTransform = this.node.getComponent(UITransform);
        if (uiTransform) {
            // 设置为UI层级，避免与游戏物理层交互
            uiTransform.priority = GameConfig.UIPriority.lampMask + 10;
        }

        // 使用Cover节点来绘制黑色背景
        const cover = this.node.getChildByName('Cover');
        this.backgroundG = cover.getComponent(Graphics);
        cover.getComponent(UITransform).setContentSize(winSize);

        // 同样禁用Cover节点的物理交互
        cover.layer = 0; // UI层

        // 设置背景色 - 黑色遮罩
        this.backgroundG.fillColor = this.maskColor;
        this.backgroundG.fillRect(-winSize.width / 2, -winSize.height / 2, winSize.width, winSize.height);

        // 获取用于绘制圆形遮罩的Graphics组件
        this.coverG = this.node.getComponent(Graphics);
        if (!this.coverG) {
            console.error('[ENTER_ROOM_MASK] 找不到Graphics组件');
            return;
        }

        // 初始半径覆盖整个屏幕，确保完全覆盖
        this.currentRadius = Math.max(winSize.width, winSize.height) * 1.2;

        console.log(`[ENTER_ROOM_MASK] start() 初始化完成，初始半径: ${this.currentRadius}`);
        console.log(`[ENTER_ROOM_MASK] 🔒 已禁用物理交互，节点层级: ${this.node.layer}`);
    }

    /**
     * 开始进入房间动画
     * @param player 玩家节点
     * @param entranceUIPos 入口在UI坐标系中的位置
     * @param onComplete 动画完成回调
     */
    public startEnterAnimation(player: Node, entranceUIPos: Vec3, onComplete: () => void) {
        this.player = player;
        this.targetPos = entranceUIPos;

        console.log(`[ENTER_ROOM_MASK] 🎬 开始进入房间动画`);
        console.log(`[ENTER_ROOM_MASK] 📍 目标UI位置: (${this.targetPos.x.toFixed(1)}, ${this.targetPos.y.toFixed(1)})`);
        console.log(`[ENTER_ROOM_MASK] ⏱️ 动画持续时间: ${this.animationDuration}秒`);
        console.log(`[ENTER_ROOM_MASK] 🔵 初始半径: ${this.currentRadius.toFixed(1)} -> 最终半径: ${this.finalRadius}`);
        console.log(`[ENTER_ROOM_MASK] 👤 玩家位置: (${this.player.position.x.toFixed(1)}, ${this.player.position.y.toFixed(1)})`);

        // 先进行一次初始绘制
        this.updateMask();

        // 立即开始动画，不再延迟
        this.startTweenAnimation(onComplete);
    }

    /**
     * 开始tween动画
     */
    private startTweenAnimation(onComplete: () => void) {
        console.log(`[ENTER_ROOM_MASK] 📍 startTweenAnimation 被调用`);

        // 开始缩小动画 - 使用自定义动画对象
        const animationTarget = { radius: this.currentRadius };

        console.log(`[ENTER_ROOM_MASK] 开始tween动画，初始半径: ${this.currentRadius}，目标半径: ${this.finalRadius}`);

        const tweenInstance = tween(animationTarget)
            .to(this.animationDuration, { radius: this.finalRadius }, {
                easing: 'quartOut', // 添加缓动效果
                onUpdate: () => {
                    this.currentRadius = animationTarget.radius;
                    this.updateMask();
                    this.updatePlayerScale();
                },
                onComplete: () => {
                    console.log(`[ENTER_ROOM_MASK] ✅ 动画完成，当前半径: ${this.currentRadius}`);
                    console.log(`[ENTER_ROOM_MASK] 准备加载隐藏房间`);

                    // 稍微延迟一下再切换场景，让玩家看到最终效果
                    this.scheduleOnce(() => {
                        onComplete();
                    }, 0.2);
                }
            });

        console.log(`[ENTER_ROOM_MASK] 🚀 tween实例创建完成，开始启动`);
        tweenInstance.start();
        console.log(`[ENTER_ROOM_MASK] 🎯 tween.start() 已调用`);
    }

    /**
     * 更新遮罩绘制 - 使用反向蒙版，参考GameoverMask的实现
     */
    private updateMask() {
        if (!this.coverG) {
            console.warn(`[ENTER_ROOM_MASK] ⚠️ Graphics组件不存在，coverG=${!!this.coverG}`);
            return;
        }

        if (this.currentRadius === undefined || !this.targetPos) {
            console.warn(`[ENTER_ROOM_MASK] ⚠️ 参数不完整: radius=${this.currentRadius}, targetPos=${this.targetPos}`);
            return;
        }

        console.log(`[ENTER_ROOM_MASK] 🎨 更新遮罩 - 中心: (${this.targetPos.x.toFixed(1)}, ${this.targetPos.y.toFixed(1)}), 半径: ${this.currentRadius.toFixed(1)}`);

        // 清除之前的绘制
        this.coverG.clear();

        // 设置填充颜色为透明（关键：这样圆形区域是透明的）
        this.coverG.fillColor = new Color(255, 255, 255, 0);

        // 绘制圆形遮罩（透明的圆形区域）
        this.coverG.circle(this.targetPos.x, this.targetPos.y, this.currentRadius);
        this.coverG.fill();
    }

    /**
     * 更新玩家缩放和位置
     */
    private updatePlayerScale() {
        if (!this.player) return;
        if (this.currentRadius === undefined) return;

        // 根据遮罩半径计算玩家缩放比例
        const maxRadius = Math.max(view.getVisibleSize().width, view.getVisibleSize().height);
        const progress = 1 - (this.currentRadius / maxRadius); // 动画进度 0-1

        // 缩放效果：从1缩放到minPlayerScale
        const scaleRatio = 1 - progress * (1 - this.minPlayerScale);
        this.player.setScale(new Vec3(scaleRatio, scaleRatio, 1));

        // 不移动玩家位置，只进行缩放效果
        console.log(`[ENTER_ROOM_MASK] 🔄 玩家缩放: ${scaleRatio.toFixed(2)}, 动画进度: ${(progress * 100).toFixed(1)}%`);
    }

    /**
     * 清理并销毁遮罩
     */
    public cleanupAndDestroy() {
        // 恢复玩家缩放
        if (this.player) {
            this.player.setScale(new Vec3(1, 1, 1));
        }

        this.node.destroy();
    }
}
