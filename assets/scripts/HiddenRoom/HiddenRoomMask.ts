import { _decorator, Color, Component, director, Graphics, Tween, tween, UITransform, view, Vec3 } from 'cc';
import { EventBus } from '../EventBus';
import { GameConfig } from '../GameConfig';
import { Map } from '../Map';
import { Player } from '../Player';
import { HiddenMap } from './HiddenMap';

const { ccclass } = _decorator;

@ccclass('HiddenRoomMask')
export class HiddenRoomMask extends Component {
    public currentRadius: number = 0; // 初始半径，动画开始前会设置
    public maskColor = new Color().fromHEX('#111111'); // 黑色遮罩
    public isAnimationReady: boolean = false; // 标识动画是否已准备好

    private g: Graphics;
    private player: Player;
    private animationCallback: Function = null;
    private focusPosition: Vec3 = new Vec3();
    private animationTween: Tween<any> = null;

    start() {
        let winSize = view.getVisibleSize();

        this.g = this.node.getComponent(Graphics);
        if (!this.g) {
            console.error(`[HiddenRoomMask] ❌ 无法获取Graphics组件！`);
            return;
        }
        console.log(`[HiddenRoomMask] ✅ Graphics组件获取成功`);

        // 底色 - 黑色遮罩背景
        let cover = this.node.getChildByName('Cover');
        if (cover) {
            let coverG = cover.getComponent(Graphics);
            cover.getComponent(UITransform).setContentSize(winSize);
            coverG.fillColor = this.maskColor;
            coverG.fillRect(-winSize.width / 2, -winSize.height / 2, winSize.width, winSize.height);
            console.log(`[HiddenRoomMask] Cover背景设置完成，尺寸: ${winSize.width}x${winSize.height}`);
        } else {
            console.warn(`[HiddenRoomMask] 找不到Cover子节点`);
        }

        // 获取玩家节点
        const playerNode = director.getScene().getChildByPath('Canvas/TiledMap/Player');
        if (playerNode) {
            this.player = playerNode.getComponent(Player);
        } else {
            console.warn('HiddenRoomMask: Player node not found during initialization');
        }

        this.node.getComponent(UITransform).priority = GameConfig.UIPriority.lampMask;

        // 在动画开始前隐藏遮罩，避免闪烁
        this.node.active = false;

        console.log(`[HiddenRoomMask] 遮罩初始化完成，初始隐藏状态`);
    }

    protected onDestroy(): void {
        EventBus.targetOff(this);
        // 停止动画
        if (this.animationTween) {
            this.animationTween.stop();
            this.animationTween = null;
        }
    }

    update(_deltaTime: number) {
        // 如果玩家节点不存在，尝试重新获取
        if (!this.player) {
            const playerNode = director.getScene().getChildByPath('Canvas/TiledMap/Player');
            if (playerNode) {
                this.player = playerNode.getComponent(Player);
            } else {
                // 如果还是找不到玩家节点，使用焦点位置绘制一个简单的圆形遮罩
                this.drawMask(this.focusPosition);
                return;
            }
        }

        // 获取玩家当前位置作为焦点（或使用设定的焦点位置）
        let focus: Vec3;
        if (this.focusPosition.equals(Vec3.ZERO)) {
            const map = this.player.node.parent.getComponent(Map)
                ? this.player.node.parent.getComponent(Map)
                : this.player.node.parent.getComponent(HiddenMap);
            focus = map.convertChildPositionToUI(this.player.node);
        } else {
            focus = this.focusPosition;
        }

        // 绘制遮罩
        this.drawMask(focus);
    }

    /**
     * 绘制遮罩效果 - 完全参考GameoverMask的实现方式
     */
    private drawMask(center: Vec3) {
        if (!this.g) {
            console.error(`[HiddenRoomMask] ❌ Graphics组件不存在，无法绘制！`);
            return;
        }

        // 清除之前的绘制
        this.g.clear();

        // 完全按照GameoverMask的方式：设置透明填充色，绘制圆形
        this.g.fillColor = new Color(255, 255, 255, 0); // 完全透明，和prefab中的设置一致
        this.g.circle(center.x, center.y, this.currentRadius);
        this.g.fill();

        // 增加调试日志频率，确保能看到绘制过程
        console.log(`[HiddenRoomMask] 🎨 绘制遮罩 - 中心: (${center.x.toFixed(1)}, ${center.y.toFixed(1)}), 半径: ${this.currentRadius.toFixed(1)}, 节点active: ${this.node.active}`);
    }

    /**
     * 开始进入隐藏房间的动画
     * @param focusPos 焦点位置（UI坐标系）
     * @param onComplete 动画完成回调
     */
    public startEnterAnimation(focusPos: Vec3, onComplete?: Function) {
        console.log(`[HiddenRoomMask] 🎬 开始进入动画，焦点位置: (${focusPos.x}, ${focusPos.y})`);

        // 激活遮罩节点，开始显示
        this.node.active = true;
        console.log(`[HiddenRoomMask] 🔛 遮罩节点已激活，active: ${this.node.active}`);

        // 设置焦点位置
        this.focusPosition = focusPos.clone();
        this.animationCallback = onComplete;

        // 从较大半径（足够覆盖屏幕）缩小到0
        const winSize = view.getVisibleSize();
        const maxRadius = Math.max(winSize.width, winSize.height); // 确保初始半径足够大
        this.currentRadius = maxRadius;

        console.log(`[HiddenRoomMask] 初始半径设置为: ${this.currentRadius}`);

        // 立即绘制一次初始状态
        this.drawMask(this.focusPosition);
        console.log(`[HiddenRoomMask] 🎨 初始绘制完成`);

        // 获取玩家节点进行缩放动画
        const playerNode = director.getScene().getChildByPath('Canvas/TiledMap/Player');
        if (playerNode) {
            console.log(`[HiddenRoomMask] 找到玩家节点，开始同步缩放动画`);

            // 同时进行遮罩和玩家缩放动画，确保完全同步
            const animationTarget = { radius: this.currentRadius, playerScale: 1.0 };
            let callbackTriggered = false; // 确保回调只触发一次

            this.animationTween = tween(animationTarget)
                .to(1.2, { radius: 0, playerScale: 0 }, { // 延长到1.2秒，让动画更平滑
                    onUpdate: (target: any, ratio: number) => {
                        this.currentRadius = target.radius;
                        // 同步缩放玩家节点
                        const scale = target.playerScale;
                        playerNode.setScale(scale, scale, scale);

                        // 当半径缩小到10左右时，触发场景切换回调
                        if (this.currentRadius <= 10 && !callbackTriggered && this.animationCallback) {
                            callbackTriggered = true;
                            console.log(`[HiddenRoomMask] 🔄 半径缩小到10，触发场景切换回调 (半径: ${this.currentRadius.toFixed(1)})`);
                            this.animationCallback();
                        }

                        // 每20%进度输出一次日志，减少日志量
                        if (Math.floor(ratio * 5) !== Math.floor((ratio - 0.01) * 5)) {
                            console.log(`[HiddenRoomMask] 动画进度: ${(ratio * 100).toFixed(0)}%, 半径: ${this.currentRadius.toFixed(1)}, 玩家缩放: ${scale.toFixed(2)}`);
                        }
                    }
                })
                .call(() => {
                    console.log(`[HiddenRoomMask] ✅ 进入动画完成`);
                    // 确保玩家缩放为0
                    playerNode.setScale(0, 0, 0);
                    // 如果由于某种原因回调还没触发，这里触发一次
                    if (!callbackTriggered && this.animationCallback) {
                        console.log(`[HiddenRoomMask] 🔄 动画完成补充触发场景切换回调`);
                        this.animationCallback();
                    }
                })
                .start();
        } else {
            console.warn(`[HiddenRoomMask] 未找到玩家节点，只执行遮罩动画`);

            // 如果找不到玩家节点，只执行遮罩动画
            const animationTarget = { radius: this.currentRadius };
            let callbackTriggered = false; // 确保回调只触发一次

            this.animationTween = tween(animationTarget)
                .to(1.2, { radius: 0 }, { // 延长到1.2秒，与有玩家时保持一致
                    onUpdate: (target: any, ratio: number) => {
                        this.currentRadius = target.radius;

                        // 当半径缩小到10左右时，触发场景切换回调
                        if (this.currentRadius <= 10 && !callbackTriggered && this.animationCallback) {
                            callbackTriggered = true;
                            console.log(`[HiddenRoomMask] 🔄 半径缩小到10，触发场景切换回调 (半径: ${this.currentRadius.toFixed(1)})`);
                            this.animationCallback();
                        }

                        // 每20%进度输出一次日志，减少日志量
                        if (Math.floor(ratio * 5) !== Math.floor((ratio - 0.01) * 5)) {
                            console.log(`[HiddenRoomMask] 动画进度: ${(ratio * 100).toFixed(0)}%, 半径: ${this.currentRadius.toFixed(1)}`);
                        }
                    }
                })
                .call(() => {
                    console.log(`[HiddenRoomMask] ✅ 进入动画完成`);
                    // 如果由于某种原因回调还没触发，这里触发一次
                    if (!callbackTriggered && this.animationCallback) {
                        console.log(`[HiddenRoomMask] 🔄 动画完成补充触发场景切换回调`);
                        this.animationCallback();
                    }
                })
                .start();
        }
    }    /**
     * 开始退出隐藏房间的动画
     * @param focusPos 焦点位置（UI坐标系）
     * @param onComplete 动画完成回调
     */
    public startExitAnimation(focusPos: Vec3, onComplete?: Function) {
        console.log(`[HiddenRoomMask] 🎬 开始退出动画，焦点位置: (${focusPos.x}, ${focusPos.y})`);

        // 激活遮罩节点，开始显示
        this.node.active = true;

        // 设置焦点位置
        this.focusPosition = focusPos.clone();
        this.animationCallback = onComplete;

        // 从0半径扩大到足够大的值
        this.currentRadius = 0;

        const winSize = view.getVisibleSize();
        const maxRadius = Math.max(winSize.width, winSize.height);

        // 获取玩家节点进行缩放动画
        const playerNode = director.getScene().getChildByPath('Canvas/TiledMap/Player');
        if (playerNode) {
            console.log(`[HiddenRoomMask] 找到玩家节点，开始退出缩放动画`);

            // 设置玩家初始缩放为0
            playerNode.setScale(0, 0, 0);

            // 同时进行遮罩和玩家缩放动画
            const animationTarget = { radius: this.currentRadius, playerScale: 0.0 };
            this.animationTween = tween(animationTarget)
                .to(0.8, { radius: maxRadius, playerScale: 1.0 }, { // 缩短为0.8秒，与进入动画保持一致
                    onUpdate: (target: any, ratio: number) => {
                        this.currentRadius = target.radius;
                        // 同步缩放玩家节点
                        const scale = target.playerScale;
                        playerNode.setScale(scale, scale, scale);

                        // 每20%进度输出一次日志，减少日志量
                        if (Math.floor(ratio * 5) !== Math.floor((ratio - 0.01) * 5)) {
                            console.log(`[HiddenRoomMask] 动画进度: ${(ratio * 100).toFixed(0)}%, 半径: ${this.currentRadius.toFixed(1)}, 玩家缩放: ${scale.toFixed(2)}`);
                        }
                    }
                })
                .call(() => {
                    console.log(`[HiddenRoomMask] ✅ 退出动画完成`);
                    // 确保玩家缩放为正常大小
                    playerNode.setScale(1, 1, 1);
                    console.log(`[HiddenRoomMask] 🔄 动画完成后确保玩家缩放为正常大小`);
                    if (this.animationCallback) {
                        this.animationCallback();
                    }
                    // 销毁遮罩节点
                    this.node.destroy();
                })
                .start();
        } else {
            console.warn(`[HiddenRoomMask] 未找到玩家节点，只执行遮罩动画`);

            // 如果找不到玩家节点，只执行遮罩动画
            const animationTarget = { radius: this.currentRadius };
            this.animationTween = tween(animationTarget)
                .to(0.8, { radius: maxRadius }, { // 缩短为0.8秒，与进入动画保持一致
                    onUpdate: (target: any, ratio: number) => {
                        this.currentRadius = target.radius;
                        // 每20%进度输出一次日志，减少日志量
                        if (Math.floor(ratio * 5) !== Math.floor((ratio - 0.01) * 5)) {
                            console.log(`[HiddenRoomMask] 动画进度: ${(ratio * 100).toFixed(0)}%, 半径: ${this.currentRadius.toFixed(1)}`);
                        }
                    }
                })
                .call(() => {
                    console.log(`[HiddenRoomMask] ✅ 退出动画完成`);
                    if (this.animationCallback) {
                        this.animationCallback();
                    }
                    // 销毁遮罩节点
                    this.node.destroy();
                })
                .start();
        }
    }
}
